# ChatAdvisor Server GitLab CI/CD 配置
# 当提交信息包含 "release:" 时自动触发后端部署
# 使用本地PM2部署，无需SSH连接
# 参考 admin-frontend 的成功部署模式

variables:
  BACKEND_PORT: "53011"
  SERVICE_NAME: "chat-advisor-release"
  NODE_VERSION: "20"
  NPM_CACHE_DIR: ".npm"

cache:
  key: backend-${CI_COMMIT_REF_SLUG}
  paths:
    - .npm/
    - node_modules/

stages:
  - validate
  - build
  - deploy
  - test
  - notify

workflow:
  rules:
    - if: $CI_COMMIT_MESSAGE =~ /release:/
      when: always
    - if: $CI_PIPELINE_SOURCE == "web"
      when: always
    - when: never

validate_backend:
  stage: validate
  tags:
    - Eva
  script:
    - "echo '🔍 验证后端部署环境...'"
    - "echo '提交信息: $CI_COMMIT_MESSAGE'"
    - "echo '分支: $CI_COMMIT_REF_NAME'"
    - "echo '项目: ChatAdvisor Server'"
    - "node --version"
    - "npm --version"
    - "echo '✅ 后端环境验证完成'"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

build_backend:
  stage: build
  tags:
    - Eva
  script:
    - "echo '🏗️ 构建后端项目...'"
    - "npm ci --cache .npm --prefer-offline"
    - "echo '🔍 代码质量检查...'"
    - "npm run lint || echo '⚠️ 代码质量检查发现问题，但继续构建'"
    - "echo '🏗️ TypeScript编译...'"
    - "npm run build"
    - "echo '📦 构建产物信息:'"
    - "du -sh dist/"
    - "ls -la dist/"
    - "echo '✅ 后端构建完成'"
  artifacts:
    name: "backend-$CI_COMMIT_SHORT_SHA"
    paths:
      - dist/
      - package.json
      - pm2.config.cjs
      - .env
    expire_in: 1 hour
  cache:
    key: backend-${CI_COMMIT_REF_SLUG}
    paths:
      - node_modules/
      - .npm/
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

deploy_backend:
  stage: deploy
  tags:
    - Eva
  dependencies:
    - build_backend
  before_script:
    - "echo '🚀 准备部署后端到生产环境...'"
    - "echo '📦 验证构建产物...'"
    - "ls -la dist/"
    - "du -sh dist/"
  script:
    - "echo '🔄 部署后端构建产物...'"
    - |
      set -e

      # 创建必要的目录
      echo "📁 创建必要的目录..."
      mkdir -p logs

      # 备份当前版本（如果存在）
      if [ -d "dist.current" ]; then
        echo "📦 备份当前版本..."
        mv dist.current dist.backup.$(date +%Y%m%d_%H%M%S)
        # 保留最近3个备份
        ls -dt dist.backup.* 2>/dev/null | tail -n +4 | xargs rm -rf || true
      fi

      # 移动新构建产物到当前版本
      echo "📂 部署新的构建产物..."
      mv dist dist.current

      echo "🔍 验证构建产物..."
      if [ ! -d "dist.current" ] || [ ! -f "dist.current/src/index.js" ]; then
        echo "❌ 构建产物验证失败"
        exit 1
      fi

      echo "📊 构建产物信息:"
      du -sh dist.current/
      ls -la dist.current/

      echo "🔄 使用PM2重启后端服务..."

      # 停止现有服务
      pm2 stop $SERVICE_NAME 2>/dev/null || echo "后端服务未运行"

      # 启动服务
      pm2 start pm2.config.cjs --only $SERVICE_NAME

      # 保存PM2配置
      pm2 save

      echo "⏳ 等待服务启动..."
      sleep 10

      echo "📊 检查PM2服务状态..."
      pm2 status $SERVICE_NAME

      echo "✅ 后端部署完成"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

health_check_backend:
  stage: test
  tags:
    - Eva
  dependencies:
    - deploy_backend
  script:
    - "echo '🏥 执行后端健康检查...'"
    - |
      echo "检查后端服务 (端口 $BACKEND_PORT)..."
      for i in {1..10}; do
        if curl -f -s "http://localhost:$BACKEND_PORT/health" > /dev/null; then
          echo "✅ 后端服务运行正常"
          break
        elif curl -f -s "http://localhost:$BACKEND_PORT" > /dev/null; then
          echo "✅ 后端服务运行正常（根路径）"
          break
        else
          echo "⏳ 等待后端服务启动... ($i/10)"
          sleep 3
        fi

        if [ $i -eq 10 ]; then
          echo "❌ 后端服务健康检查失败"
          exit 1
        fi
      done

      echo "🔍 检查API响应..."
      api_response=$(curl -s "http://localhost:$BACKEND_PORT/health" 2>/dev/null || curl -s "http://localhost:$BACKEND_PORT" 2>/dev/null || echo "")
      if [ -n "$api_response" ]; then
        echo "✅ API响应正常"
      else
        echo "⚠️ 无法获取API响应"
      fi

      echo "⏱️ 检查响应时间..."
      response_time=$(curl -o /dev/null -s -w "%{time_total}" "http://localhost:$BACKEND_PORT" 2>/dev/null || echo "0")
      echo "后端响应时间: ${response_time}秒"

      echo "🎉 后端健康检查完成"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

pm2_status_check:
  stage: test
  tags:
    - Eva
  dependencies:
    - health_check_backend
  script:
    - "echo '🔍 检查PM2服务状态...'"
    - |
      echo "📊 PM2服务状态:"
      pm2 status

      echo ""
      echo "📋 后端服务详细信息:"
      pm2 show $SERVICE_NAME || echo "⚠️ 无法获取后端服务详细信息"

      echo ""
      echo "📝 后端服务日志 (最近10行):"
      pm2 logs $SERVICE_NAME --lines 10 --nostream || echo "⚠️ 无法获取后端服务日志"
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

notify_success:
  stage: notify
  tags:
    - Eva
  dependencies:
    - pm2_status_check
  script:
    - "echo '🎉 后端部署成功通知'"
    - |
      echo "后端部署信息:"
      echo "- 项目: ChatAdvisor Server"
      echo "- 提交: $CI_COMMIT_SHORT_SHA"
      echo "- 分支: $CI_COMMIT_REF_NAME"
      echo "- 服务: $SERVICE_NAME"
      echo "- 访问地址: http://localhost:$BACKEND_PORT"
      echo "- 健康检查: http://localhost:$BACKEND_PORT/health"
      echo "- 流水线: $CI_PIPELINE_URL"
      echo ""
      echo "PM2管理命令:"
      echo "- 查看状态: pm2 status"
      echo "- 查看日志: pm2 logs $SERVICE_NAME"
      echo "- 重启服务: pm2 restart $SERVICE_NAME"
  when: on_success
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/

notify_failure:
  stage: notify
  tags:
    - Eva
  script:
    - "echo '❌ 后端部署失败通知'"
    - |
      echo "后端部署失败信息:"
      echo "- 项目: ChatAdvisor Server"
      echo "- 提交: $CI_COMMIT_SHORT_SHA"
      echo "- 分支: $CI_COMMIT_REF_NAME"
      echo "- 流水线: $CI_PIPELINE_URL"
      echo ""
      echo "请检查:"
      echo "1. 构建日志中的错误信息"
      echo "2. PM2服务的运行状态"
      echo "3. 后端服务的日志输出"
      echo "4. 本地环境的配置状态"
  when: on_failure
  only:
    variables:
      - $CI_COMMIT_MESSAGE =~ /release:/
